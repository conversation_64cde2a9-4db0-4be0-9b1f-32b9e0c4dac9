/**
 * Sayfa Geçiş Yükleme Çubuğu CSS
 * YouTube tarzı üst kısımda görünen progress bar
 */

/* Yükleme Çubuğu Konteyneri */
.page-loading-bar {
    position: fixed;
    top: 0;
    left: 0;
    width: 100%;
    height: 3px;
    z-index: 99999;
    background-color: transparent;
    pointer-events: none;
    opacity: 0;
    transition: opacity 0.3s ease;
}

/* Admin bar varsa yükleme çubuğunu aşağı kaydır */
body.admin-bar .page-loading-bar {
    top: 32px;
}

/* Mobil admin bar için */
@media (max-width: 782px) {
    body.admin-bar .page-loading-bar {
        top: 46px;
    }
}

/* Yükleme Çubuğu Aktif Durumu */
.page-loading-bar.active {
    opacity: 1;
}

/* Progress Bar */
.page-loading-bar .progress-bar {
    height: 100%;
    width: 0%;
    background: linear-gradient(90deg, 
        #667eea 0%, 
        #764ba2 25%, 
        #f093fb 50%, 
        #4<PERSON><PERSON>fe 75%, 
        #00d4aa 100%);
    background-size: 200% 100%;
    animation: gradient-shift 2s ease-in-out infinite;
    transition: width 0.3s cubic-bezier(0.4, 0, 0.2, 1);
    border-radius: 0 3px 3px 0;
    box-shadow: 0 0 10px rgba(102, 126, 234, 0.5);
}

/* Gradient Animasyonu */
@keyframes gradient-shift {
    0% {
        background-position: 200% 0;
    }
    100% {
        background-position: -200% 0;
    }
}

/* Yükleme Çubuğu Glow Efekti */
.page-loading-bar .progress-bar::after {
    content: '';
    position: absolute;
    top: 0;
    right: 0;
    width: 20px;
    height: 100%;
    background: linear-gradient(90deg, transparent, rgba(255, 255, 255, 0.8), transparent);
    animation: shine 1.5s ease-in-out infinite;
}

/* Shine Animasyonu */
@keyframes shine {
    0% {
        transform: translateX(-20px);
        opacity: 0;
    }
    50% {
        opacity: 1;
    }
    100% {
        transform: translateX(20px);
        opacity: 0;
    }
}

/* Dark Mode için Yükleme Çubuğu */
body.tutor-dark-mode .page-loading-bar .progress-bar,
html[data-theme="dark"] .page-loading-bar .progress-bar {
    box-shadow: 0 0 15px rgba(102, 126, 234, 0.7);
}

/* Yükleme Çubuğu Tamamlanma Animasyonu */
.page-loading-bar.complete .progress-bar {
    width: 100% !important;
    transition: width 0.2s ease-out;
}

.page-loading-bar.complete {
    animation: fadeOut 0.5s ease-out 0.3s forwards;
}

@keyframes fadeOut {
    to {
        opacity: 0;
        transform: translateY(-3px);
    }
}

/* Pulse Efekti - Yavaş Bağlantılarda */
.page-loading-bar.slow .progress-bar {
    animation: gradient-shift 2s ease-in-out infinite, pulse 1s ease-in-out infinite;
}

@keyframes pulse {
    0%, 100% {
        box-shadow: 0 0 10px rgba(102, 126, 234, 0.5);
    }
    50% {
        box-shadow: 0 0 20px rgba(102, 126, 234, 0.8);
    }
}

/* Hızlı Geçiş Modu */
.page-loading-bar.fast .progress-bar {
    transition: width 0.1s linear;
}

/* Yükleme Çubuğu Hata Durumu */
.page-loading-bar.error .progress-bar {
    background: linear-gradient(90deg, #ff6b6b 0%, #ee5a52 100%);
    box-shadow: 0 0 10px rgba(255, 107, 107, 0.5);
    animation: error-pulse 0.5s ease-in-out 3;
}

@keyframes error-pulse {
    0%, 100% {
        opacity: 1;
    }
    50% {
        opacity: 0.7;
    }
}

/* Responsive Tasarım */
@media (max-width: 768px) {
    .page-loading-bar {
        height: 2px;
    }
    
    .page-loading-bar .progress-bar {
        border-radius: 0 2px 2px 0;
    }
}

/* Yüksek Performans için GPU Hızlandırması */
.page-loading-bar,
.page-loading-bar .progress-bar {
    will-change: transform, opacity, width;
    transform: translateZ(0);
    backface-visibility: hidden;
}

/* Erişilebilirlik - Hareket Azaltma Tercihi */
@media (prefers-reduced-motion: reduce) {
    .page-loading-bar .progress-bar {
        animation: none;
        transition: width 0.3s ease;
    }
    
    .page-loading-bar .progress-bar::after {
        animation: none;
    }
    
    .page-loading-bar.complete {
        animation: none;
        transition: opacity 0.3s ease;
    }
}

/* Yükleme Çubuğu Metin Göstergesi (Opsiyonel) */
.page-loading-bar .loading-text {
    position: absolute;
    top: 5px;
    right: 10px;
    font-size: 10px;
    color: var(--modern-primary);
    font-weight: 500;
    opacity: 0.8;
    font-family: 'Inter', sans-serif;
}

/* Dark Mode için Metin */
body.tutor-dark-mode .page-loading-bar .loading-text,
html[data-theme="dark"] .page-loading-bar .loading-text {
    color: #ffffff;
}

/* Yükleme Çubuğu Başlangıç Animasyonu */
.page-loading-bar.starting .progress-bar {
    animation: start-loading 0.5s ease-out;
}

@keyframes start-loading {
    0% {
        width: 0%;
        opacity: 0;
    }
    50% {
        opacity: 1;
    }
    100% {
        width: 10%;
        opacity: 1;
    }
}

/* Yükleme Çubuğu Duraklatma */
.page-loading-bar.paused .progress-bar {
    animation-play-state: paused;
    transition: none;
}

/* Yükleme Çubuğu Hızlandırma */
.page-loading-bar.accelerating .progress-bar {
    transition: width 0.1s ease-out;
}
