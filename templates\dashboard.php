<?php
/**
 * Template for displaying frontend dashboard
 *
 * @package Tu<PERSON>\Templates
 * <AUTHOR> <<EMAIL>>
 * @link https://themeum.com
 * @since 1.4.3
 */

$is_by_short_code = isset( $is_shortcode ) && true === $is_shortcode;
if ( ! $is_by_short_code && ! defined( 'OTLMS_VERSION' ) ) {
    // Özel header - tema header'ı olmadan
    ?>
    <!doctype html>
    <html <?php language_attributes(); ?>>
    <head>
        <meta charset="<?php bloginfo( 'charset' ); ?>" />
        <meta name="viewport" content="width=device-width, initial-scale=1" />
        <link rel="profile" href="https://gmpg.org/xfn/11" />
        <?php wp_head(); ?>

        <!-- Modern Dashboard CSS -->
        <link rel="stylesheet" href="<?php echo plugin_dir_url(__FILE__) . '../assets/css/page-loading-bar.css'; ?>" />

        <style>
            /* <PERSON><PERSON> kartları için border ve box-shadow düzenlemeleri */
            .tutor-card:not(.tutor-no-border) {
                border: 1px solid #cdcfd536 !important;
                box-shadow: 0 0 50px rgba(0, 0, 0, 0.05) !important;
            }

            /* Tema modu geçiş butonu konteyneri */
            .tutor-theme-mode-toggle {
                display: flex;
                align-items: center;
                margin-right: 15px;
                position: relative;
                cursor: pointer;
            }

            /* Tema modu buton stili */
            .tutor-theme-mode-button {
                display: flex;
                align-items: center;
                justify-content: center;
                width: 40px;
                height: 40px;
                border-radius: 50%;
                background-color: transparent;
                border: none;
                cursor: pointer;
                transition: all 0.3s ease;
                color: var(--tutor-color-primary);
            }

            .tutor-theme-mode-button:hover .tutor-theme-mode-icon {
                transform: scale(1.2);
            }

            /* Tema modu buton ikonları */
            .tutor-theme-mode-icon {
                width: 24px;
                height: 24px;
                stroke: var(--tutor-color-primary);
                fill: none;
                stroke-width: 2;
                stroke-linecap: round;
                stroke-linejoin: round;
                transition: transform 0.3s ease;
            }

            /* Güneş ikonu - gündüz modu */
            .tutor-theme-mode-icon-sun {
                display: none;
            }

            /* Hilal ikonu - gece modu */
            .tutor-theme-mode-icon-moon {
                display: none;
            }

            /* Gündüz modunda güneş ikonunu göster */
            html:not([data-theme="dark"]) .tutor-theme-mode-icon-sun {
                display: block;
            }

            /* Gece modunda hilal ikonunu göster */
            html[data-theme="dark"] .tutor-theme-mode-icon-moon {
                display: block;
            }

            /* Karanlık mod stilleri */
            html[data-theme="dark"],
            body.tutor-dark-mode {
                --tutor-body-bg: #0F0F0F;
                --tutor-card-bg: #121212;
                --tutor-text-color: #F5F5F5;
                --tutor-text-color-secondary: #BBBBBB;
                --tutor-border-color: #2A2A2A;
                --tutor-shadow-color: rgba(0, 0, 0, 0.3);
            }

            /* Sayfa yüklenmeden önce dark mod stillerini uygula */
            html[data-theme="dark"] {
                background-color: var(--tutor-body-bg) !important;
            }

            /* Karanlık mod - genel arka plan */
            html[data-theme="dark"] body,
            body.tutor-dark-mode.tutor-dashboard-page {
                background-color: var(--tutor-body-bg) !important;
                color: var(--tutor-text-color) !important;
            }

            /* Geçiş efektleri - dark mod toggle için */
            html:not([data-theme-transitioning]) .tutor-card,
            html:not([data-theme-transitioning]) .tutor-dashboard-left-menu,
            html:not([data-theme-transitioning]) .tutor-frontend-dashboard-header,
            html:not([data-theme-transitioning]) .tutor-btn,
            html:not([data-theme-transitioning]) .tutor-form-control {
                transition: background-color 0.3s ease, color 0.3s ease, border-color 0.3s ease, box-shadow 0.3s ease;
            }

            /* Karanlık mod - kartlar */
            html[data-theme="dark"] .tutor-card:not(.tutor-no-border),
            body.tutor-dark-mode .tutor-card:not(.tutor-no-border) {
                background-color: var(--tutor-card-bg) !important;
                border: 1px solid #2c304636 !important;
                box-shadow: 0 0 50px rgba(0, 0, 0, 0.1) !important;
            }

            /* Dashboard genel arka plan rengi düzeltmesi - sadece dark modda */
            body.tutor-dark-mode,
            html[data-theme="dark"],
            body.tutor-dark-mode .tutor-wrap,
            html[data-theme="dark"] .tutor-wrap,
            body.tutor-dark-mode .tutor-dashboard,
            html[data-theme="dark"] .tutor-dashboard,
            body.tutor-dark-mode .tutor-dashboard-page,
            html[data-theme="dark"] .tutor-dashboard-page,
            body.tutor-dark-mode #tutor-page-wrap,
            html[data-theme="dark"] #tutor-page-wrap {
                background-color: #0f0f0f !important;
            }

            /* Dashboard genel arka plan rengi - gündüz modunda */
            body.tutor-dashboard-page,
            .tutor-wrap,
            .tutor-dashboard,
            #tutor-page-wrap {
                background-color: #ffffff !important;
            }

            /* Karanlık mod - metin renkleri */
            html[data-theme="dark"] .tutor-color-black,
            html[data-theme="dark"] .tutor-fs-5,
            html[data-theme="dark"] .tutor-fs-4,
            html[data-theme="dark"] .tutor-fs-3,
            html[data-theme="dark"] h1,
            html[data-theme="dark"] h2,
            html[data-theme="dark"] h3,
            html[data-theme="dark"] h4,
            html[data-theme="dark"] h5,
            html[data-theme="dark"] h6,
            html[data-theme="dark"] p,
            html[data-theme="dark"] .tutor-course-card-title,
            html[data-theme="dark"] .tutor-course-card-title a,
            body.tutor-dark-mode .tutor-color-black,
            body.tutor-dark-mode .tutor-fs-5,
            body.tutor-dark-mode .tutor-fs-4,
            body.tutor-dark-mode .tutor-fs-3,
            body.tutor-dark-mode h1,
            body.tutor-dark-mode h2,
            body.tutor-dark-mode h3,
            body.tutor-dark-mode h4,
            body.tutor-dark-mode h5,
            body.tutor-dark-mode h6,
            body.tutor-dark-mode p,
            body.tutor-dark-mode .tutor-course-card-title,
            body.tutor-dark-mode .tutor-course-card-title a,
            html[data-theme="dark"] .tutor-course-card .tutor-course-name,
            html[data-theme="dark"] .tutor-course-card .tutor-course-name a,
            body.tutor-dark-mode .tutor-course-card .tutor-course-name,
            body.tutor-dark-mode .tutor-course-card .tutor-course-name a {
                color: var(--tutor-text-color) !important;
            }

            /* Karanlık mod - ikincil metin renkleri */
            html[data-theme="dark"] .tutor-color-muted,
            html[data-theme="dark"] .tutor-color-secondary,
            html[data-theme="dark"] .tutor-ratings-count,
            html[data-theme="dark"] .tutor-course-card-meta,
            html[data-theme="dark"] .tutor-course-completion-text,
            body.tutor-dark-mode .tutor-color-muted,
            body.tutor-dark-mode .tutor-color-secondary,
            body.tutor-dark-mode .tutor-ratings-count,
            body.tutor-dark-mode .tutor-course-card-meta,
            body.tutor-dark-mode .tutor-course-completion-text {
                color: var(--tutor-text-color-secondary) !important;
            }

            /* Karanlık mod - header ve sidebar */
            html[data-theme="dark"] .tutor-frontend-dashboard-header,
            html[data-theme="dark"] .tutor-dashboard-left-menu,
            body.tutor-dark-mode .tutor-frontend-dashboard-header,
            body.tutor-dark-mode .tutor-dashboard-left-menu {
                background-color: var(--tutor-card-bg) !important;
                border-color: var(--tutor-border-color) !important;
            }

            /* Dashboard header arka plan rengi düzeltmesi */
            body.tutor-dark-mode .tutor-frontend-dashboard-header,
            body.tutor-dark-mode #tutor-page-wrap > div > div.tutor-container > div.tutor-row.tutor-d-flex.tutor-justify-between.tutor-frontend-dashboard-header {
                background-color: #0f0f0f !important;
            }

            /* Dashboard logo arka plan rengi düzeltmesi */
            body.tutor-dark-mode .tutor-dashboard-logo,
            html[data-theme="dark"] .tutor-dashboard-logo {
                background-color: #121212 !important;
            }

            /* Dashboard başlık rengi düzeltmesi */
            body.tutor-dark-mode .tutor-fs-5.tutor-fw-medium.tutor-color-black.tutor-text-capitalize.tutor-mb-24,
            html[data-theme="dark"] .tutor-fs-5.tutor-fw-medium.tutor-color-black.tutor-text-capitalize.tutor-mb-24 {
                color: #ffffff !important;
            }

            /* Dashboard kart içerikleri ve diğer elementler */
            body.tutor-dark-mode .tutor-dashboard-content-inner,
            html[data-theme="dark"] .tutor-dashboard-content-inner,
            body.tutor-dark-mode .tutor-dashboard-content,
            html[data-theme="dark"] .tutor-dashboard-content,
            body.tutor-dark-mode .tutor-col-12,
            html[data-theme="dark"] .tutor-col-12,
            body.tutor-dark-mode .tutor-col,
            html[data-theme="dark"] .tutor-col,
            body.tutor-dark-mode .tutor-col-auto,
            html[data-theme="dark"] .tutor-col-auto {
                background-color: transparent !important;
            }

            /* Dashboard kart başlıkları ve alt başlıkları */
            body.tutor-dark-mode .tutor-dashboard-inline-links,
            html[data-theme="dark"] .tutor-dashboard-inline-links,
            body.tutor-dark-mode .tutor-dashboard-header-stats,
            html[data-theme="dark"] .tutor-dashboard-header-stats,
            body.tutor-dark-mode .tutor-dashboard-header-button-list,
            html[data-theme="dark"] .tutor-dashboard-header-button-list {
                background-color: #0f0f0f !important;
                border-color: #2A2A2A !important;
            }

            /* Dashboard içerik bölümü */
            body.tutor-dark-mode .tutor-dashboard-content-inner,
            html[data-theme="dark"] .tutor-dashboard-content-inner {
                background-color: #0f0f0f !important;
                border-color: #2A2A2A !important;
            }

            /* Dashboard kartları ve içerik bölümleri */
            body.tutor-dark-mode .tutor-card,
            html[data-theme="dark"] .tutor-card,
            body.tutor-dark-mode .tutor-card-body,
            html[data-theme="dark"] .tutor-card-body,
            body.tutor-dark-mode .tutor-card-footer,
            html[data-theme="dark"] .tutor-card-footer {
                background-color: #121212 !important;
                border-color: #2A2A2A !important;
            }

            /* Dashboard içerik tablosu kenarlık rengi */
            body.tutor-dark-mode .tutor-dashboard-content-inner table,
            html[data-theme="dark"] .tutor-dashboard-content-inner table {
                border: 1px solid #0f0f0f !important;
            }

            /* Dashboard sayfa arka planı - sadece dark modda */
            body.tutor-dark-mode.tutor-dashboard-page,
            body.tutor-dark-mode.tutor-dashboard-page #tutor-page-wrap,
            html[data-theme="dark"] body.tutor-dashboard-page,
            html[data-theme="dark"] body.tutor-dashboard-page #tutor-page-wrap {
                background-color: #0f0f0f !important;
            }

            /* Dashboard sayfa arka planı - gündüz modunda */
            body.tutor-dashboard-page,
            body.tutor-dashboard-page #tutor-page-wrap {
                background-color: #ffffff !important;
            }

            /* Dashboard sol menü */
            body.tutor-dark-mode .tutor-dashboard-left-menu,
            html[data-theme="dark"] .tutor-dashboard-left-menu {
                background-color: #121212 !important;
                border-color: #2A2A2A !important;
            }

            /* Dashboard içerik bölümü arka planı */
            body.tutor-dark-mode .tutor-dashboard-content,
            html[data-theme="dark"] .tutor-dashboard-content {
                background-color: #0f0f0f !important;
            }

            /* Karanlık mod - tablolar */
            html[data-theme="dark"] .tutor-table th,
            body.tutor-dark-mode .tutor-table th {
                background-color: #222222 !important;
                color: var(--tutor-text-color) !important;
                border-bottom-color: var(--tutor-border-color) !important;
            }

            html[data-theme="dark"] .tutor-table td,
            body.tutor-dark-mode .tutor-table td {
                border-bottom-color: var(--tutor-border-color) !important;
                color: var(--tutor-text-color-secondary) !important;
                background: #121212 !important;
            }

            html[data-theme="dark"] .tutor-table tr:hover,
            body.tutor-dark-mode .tutor-table tr:hover {
                background-color: rgba(50, 50, 50, 0.5) !important;
            }

            html[data-theme="dark"] .tutor-table tr td,
            body.tutor-dark-mode .tutor-table tr td {
                background: #121212 !important;
            }

            /* Tablo içindeki bağlantılar için dark mod rengi */
            html[data-theme="dark"] .tutor-table tr td>a:not(.tutor-btn):not(.tutor-iconic-btn):not(.quiz-manual-review-action),
            html[data-theme="dark"] .tutor-table tr td .tutor-table-link,
            body.tutor-dark-mode .tutor-table tr td>a:not(.tutor-btn):not(.tutor-iconic-btn):not(.quiz-manual-review-action),
            body.tutor-dark-mode .tutor-table tr td .tutor-table-link {
                color: #ffffff !important;
            }

            /* Navigasyon linkleri için dark mod rengi */
            html[data-theme="dark"] .tutor-nav-link,
            body.tutor-dark-mode .tutor-nav-link {
                color: #ffffff8a !important;
            }

            /* Aktif navigasyon linkleri için dark mod rengi */
            html[data-theme="dark"] .tutor-nav-link.is-active,
            body.tutor-dark-mode .tutor-nav-link.is-active {
                color: #ffffff !important;
            }

            /* Karanlık mod - dashboard butonlar */
            html[data-theme="dark"] body.tutor-dashboard-page .tutor-btn.tutor-btn-outline-primary,
            body.tutor-dark-mode.tutor-dashboard-page .tutor-btn.tutor-btn-outline-primary {
                background-color: rgb(255 255 255 / 7%) !important;
                color: #FFFFFF !important;
                border: none !important;
            }

            html[data-theme="dark"] body.tutor-dashboard-page .tutor-btn.tutor-btn-outline-primary:hover,
            body.tutor-dark-mode.tutor-dashboard-page .tutor-btn.tutor-btn-outline-primary:hover {
                background-color: rgb(130 130 130 / 50%) !important;
            }

            /* Karanlık mod - avatar dropdown */
            html[data-theme="dark"] .tutor-avatar-dropdown,
            body.tutor-dark-mode .tutor-avatar-dropdown {
                background-color: var(--tutor-card-bg) !important;
                border-color: var(--tutor-border-color) !important;
            }

            html[data-theme="dark"] .tutor-avatar-dropdown-header,
            body.tutor-dark-mode .tutor-avatar-dropdown-header {
                background: linear-gradient(135deg, #222222, var(--tutor-card-bg)) !important;
            }

            html[data-theme="dark"] .tutor-avatar-dropdown-menu li a,
            body.tutor-dark-mode .tutor-avatar-dropdown-menu li a {
                color: var(--tutor-text-color) !important;
            }

            html[data-theme="dark"] .tutor-avatar-dropdown-menu li a:hover,
            body.tutor-dark-mode .tutor-avatar-dropdown-menu li a:hover {
                background-color: rgba(50, 50, 50, 0.5) !important;
            }

            /* Karanlık mod - ilerleme çubuğu */
            html[data-theme="dark"] .tutor-progress-bar,
            body.tutor-dark-mode .tutor-progress-bar {
                background-color: #333333 !important;
            }

            /* Tema modu geçiş butonu - animasyon */
            .tutor-theme-transition {
                animation: tutor-theme-pulse 0.5s cubic-bezier(0.4, 0, 0.6, 1);
            }

            @keyframes tutor-theme-pulse {
                0%, 100% {
                    opacity: 1;
                    transform: scale(1);
                }
                50% {
                    opacity: 0.85;
                    transform: scale(0.98);
                }
            }

            /* Hamburger menu button ve sayfa başlığı karanlık mod */
            body.tutor-dark-mode .tutor-hamburger-menu-btn,
            html[data-theme="dark"] .tutor-hamburger-menu-btn {
                color: #ffffff !important;
            }

            body.tutor-dark-mode .tutor-current-page-title,
            html[data-theme="dark"] .tutor-current-page-title {
                color: #ffffff !important;
            }

            /* Responsive hamburger pozisyonu */
            @media (max-width: 992px) {
                .hamburger-container {
                    left: 10px !important;
                }
            }

            /* Sayfa başlıklarını gizle - hamburger butonunda zaten gösteriyoruz */

            /* Kayıtlı Kurslar sayfası başlığı - spesifik seçici */
            #tutor-page-wrap > div > div.tutor-container > div.tutor-row.tutor-frontend-dashboard-maincontent > div.tutor-col-12.tutor-col-md-8.tutor-col-lg-9 > div > div.tutor-fs-5.tutor-fw-medium.tutor-color-black.tutor-mb-16.tutor-text-capitalize {
                display: none !important;
                visibility: hidden !important;
                opacity: 0 !important;
            }

            /* Profilim sayfası başlığı */
            .tutor-dashboard-content-inner .tutor-fs-5.tutor-fw-medium.tutor-color-black.tutor-mb-24.tutor-profile-title,
            .tutor-fs-5.tutor-fw-medium.tutor-color-black.tutor-mb-24.tutor-profile-title,
            .tutor-profile-title,

            /* Kayıtlı Kurslar sayfası başlığı - genel seçiciler */
            .tutor-dashboard-content-inner .tutor-fs-5.tutor-fw-medium.tutor-color-black.tutor-mb-16.tutor-text-capitalize,
            .tutor-fs-5.tutor-fw-medium.tutor-color-black.tutor-mb-16.tutor-text-capitalize,

            /* Test Katılımları ve Kurs Duyuruları sayfası başlığı */
            .tutor-dashboard-content-inner .tutor-fs-5.tutor-fw-medium.tutor-color-black.tutor-mb-24,
            .tutor-fs-5.tutor-fw-medium.tutor-color-black.tutor-mb-24:not(.tutor-current-page-title),

            /* Ayarlar sayfası başlığı */
            .tutor-dashboard-content-inner .tutor-fs-4.tutor-fw-medium.tutor-mb-24,
            .tutor-fs-4.tutor-fw-medium.tutor-mb-24:not(.tutor-current-page-title),

            /* Genel dashboard başlıkları */
            body.tutor-dashboard-page .tutor-dashboard-content-inner > .tutor-fs-5.tutor-fw-medium.tutor-color-black,
            body.tutor-dashboard-page .tutor-dashboard-content-inner > .tutor-fs-4.tutor-fw-medium {
                display: none !important;
                visibility: hidden !important;
                opacity: 0 !important;
            }
        </style>
        <?php
        // localStorage kullanıldığı için PHP tarafında tema modu kontrolü yapılmıyor
        // Varsayılan değerler tanımlanıyor
        $theme_mode = 'light';
        $is_dark_mode = false;

        // Nonce oluştur - localStorage kullanıldığı için artık gerekli değil ama uyumluluk için bırakıldı
        $theme_mode_nonce = wp_create_nonce('tutor_theme_mode_nonce');
        ?>

        <script>
            // Sayfa yüklenmeden önce dark mod ayarlarını uygula
            // Bu kısım sayfa geçişlerinde beyaz yanıp sönmeyi önler
            (function() {
                const DARK_MODE_CLASS = 'tutor-dark-mode';
                const STORAGE_KEY = 'tutor_theme_mode';

                // localStorage'dan tema modu tercihini al
                let savedThemeMode = localStorage.getItem(STORAGE_KEY);

                // Eğer localStorage'da kayıtlı değer yoksa, varsayılan olarak 'light' kullan
                if (!savedThemeMode) {
                    savedThemeMode = 'light';
                    localStorage.setItem(STORAGE_KEY, savedThemeMode);
                }

                // Tema modu bilgilerini global değişkene aktar
                window.tutorThemeMode = {
                    current: savedThemeMode
                };

                const isDarkMode = savedThemeMode === 'dark';
                console.log('Tema modu belirlendi (localStorage):', isDarkMode ? 'dark' : 'light');

                if (isDarkMode) {
                    document.documentElement.setAttribute('data-theme', 'dark');
                    // Sayfa içeriği yüklenmeden önce dark mod stillerini uygula
                    var style = document.createElement('style');
                    style.setAttribute('data-no-transition', 'true');
                    style.textContent = 'html[data-theme="dark"] { background-color: #0F0F0F !important; } html[data-theme="dark"] *, body { transition: none !important; }';
                    document.head.appendChild(style);

                    // Body'e dark mode sınıfını ekle
                    document.addEventListener('DOMContentLoaded', function() {
                        document.body.classList.add(DARK_MODE_CLASS);
                    });
                }
            })();
        </script>
        <script>
            // Tema modu değişikliği için JavaScript
            document.addEventListener('DOMContentLoaded', function() {
                // Tema modu değişkenleri
                const DARK_MODE_CLASS = 'tutor-dark-mode';
                const STORAGE_KEY = 'tutor_theme_mode';

                // Tema modu butonunu oluştur ve ekle
                function createThemeModeToggle() {
                    // Tema modu butonunu bul
                    const themeModeButton = document.getElementById('tutor-theme-mode-button');

                    if (!themeModeButton) {
                        return;
                    }

                    // Tema modu değişikliği için event listener ekle
                    themeModeButton.addEventListener('click', function() {
                        // Mevcut tema modunu al
                        const currentThemeMode = window.tutorThemeMode.current;

                        // Yeni tema modu değeri
                        const newThemeMode = currentThemeMode === 'dark' ? 'light' : 'dark';

                        // Önce tema modunu uygula
                        applyThemeMode(newThemeMode);

                        // Tema değişikliği animasyonu
                        animateThemeChange(newThemeMode);

                        // localStorage'a kaydet
                        updateThemeMode(newThemeMode);
                    });
                }

                // Tema modunu localStorage'a kaydet
                function updateThemeMode(mode) {
                    // Tema modu bilgilerini hemen güncelle
                    window.tutorThemeMode.current = mode;

                    // localStorage'a kaydet
                    localStorage.setItem(STORAGE_KEY, mode);
                    console.log('Tema modu localStorage\'a kaydedildi:', mode);
                }

                // Tema modunu uygula
                function applyThemeMode(mode) {
                    if (mode === 'dark') {
                        // Dark mode
                        document.documentElement.setAttribute('data-theme', 'dark');
                        document.body.classList.add(DARK_MODE_CLASS);
                    } else {
                        // Light mode
                        document.documentElement.removeAttribute('data-theme');
                        document.body.classList.remove(DARK_MODE_CLASS);
                    }
                }

                // Tema değişikliği animasyonu
                function animateThemeChange(mode) {
                    // Tema değişikliği butonuna animasyon ekle
                    const themeToggleBtn = document.querySelector('.tutor-theme-mode-button');

                    if (themeToggleBtn) {
                        // Buton animasyonu
                        themeToggleBtn.style.animation = 'theme-toggle-scale 0.5s cubic-bezier(0.68, -0.55, 0.27, 1.55)';

                        // Animasyon tamamlandıktan sonra temizle
                        setTimeout(() => {
                            themeToggleBtn.style.animation = '';
                        }, 500);
                    }

                    // Sayfaya geçiş efekti ekle
                    document.body.classList.add('theme-transition-active');
                    setTimeout(() => {
                        document.body.classList.remove('theme-transition-active');
                    }, 500);

                    // Tüm kartları seç
                    const cards = document.querySelectorAll('.tutor-card');

                    // Her kart için animasyon uygula
                    cards.forEach((card, index) => {
                        // Gecikme süresi - kartlar sırayla animasyon göstersin
                        const delay = index * 50;

                        // Geçici animasyon sınıfı ekle
                        setTimeout(() => {
                            card.classList.add('tutor-theme-transition');

                            // Animasyon tamamlandıktan sonra sınıfı kaldır
                            setTimeout(() => {
                                card.classList.remove('tutor-theme-transition');
                            }, 500);
                        }, delay);
                    });
                }

                // Tema modu butonunu oluştur
                createThemeModeToggle();

                // Sayfa yüklendikten sonra geçiş efektlerini aç
                setTimeout(function() {
                    // Geçiş efektlerini kapatıp açmak için kullanılan stil elementini kaldır
                    const noTransitionStyle = document.querySelector('style[data-no-transition]');
                    if (noTransitionStyle) {
                        noTransitionStyle.remove();
                    }
                }, 500); // 500ms sonra geçiş efektlerini aç
            });
        </script>
    </head>
    <body <?php body_class('tutor-dashboard-page'); ?>>
        <div id="tutor-page-wrap" class="tutor-site-wrap site">
    <?php
}

global $wp_query;

$dashboard_page_slug = '';
$dashboard_page_name = '';
if ( isset( $wp_query->query_vars['tutor_dashboard_page'] ) && $wp_query->query_vars['tutor_dashboard_page'] ) {
	$dashboard_page_slug = $wp_query->query_vars['tutor_dashboard_page'];
	$dashboard_page_name = $wp_query->query_vars['tutor_dashboard_page'];
}
/**
 * Getting dashboard sub pages
 */
if ( isset( $wp_query->query_vars['tutor_dashboard_sub_page'] ) && $wp_query->query_vars['tutor_dashboard_sub_page'] ) {
	$dashboard_page_name = $wp_query->query_vars['tutor_dashboard_sub_page'];
	if ( $dashboard_page_slug ) {
		$dashboard_page_name = $dashboard_page_slug . '/' . $dashboard_page_name;
	}
}
$dashboard_page_name = apply_filters( 'tutor_dashboard_sub_page_template', $dashboard_page_name );

$user_id                   = get_current_user_id();
$user                      = get_user_by( 'ID', $user_id );
$enable_profile_completion = tutor_utils()->get_option( 'enable_profile_completion' );
$is_instructor             = tutor_utils()->is_instructor();

// URLS.
$current_url = tutor()->current_url;

// Mobil footer için yeni URL'ler - Kayıtlı Kurslar, Test Katılımlarım ve Soru & Cevap
$mobile_footer_url_1 = trailingslashit( tutor_utils()->tutor_dashboard_url( 'enrolled-courses' ) );
$mobile_footer_url_2 = trailingslashit( tutor_utils()->tutor_dashboard_url( 'my-quiz-attempts' ) );
$mobile_footer_url_3 = trailingslashit( tutor_utils()->tutor_dashboard_url( 'question-answer' ) );

// Footer links.
$footer_links = array(
	array(
		'title'      => __( 'Menu', 'tutor' ),
		'url'        => '#',
		'is_active'  => false,
		'icon_class' => 'ttr tutor-icon-hamburger-o tutor-dashboard-menu-toggler',
	),
	array(
		'title'      => __( 'Kayıtlı Kurslar', 'tutor' ),
		'url'        => $mobile_footer_url_1,
		'is_active'  => $mobile_footer_url_1 == $current_url || strpos($current_url, 'enrolled-courses') !== false,
		'icon_class' => 'ttr tutor-icon-book-open',
	),
	array(
		'title'      => __( 'Soru & Cevap', 'tutor' ),
		'url'        => $mobile_footer_url_3,
		'is_active'  => $mobile_footer_url_3 == $current_url || strpos($current_url, 'question-answer') !== false,
		'icon_class' => 'ttr tutor-icon-question',
	),
	array(
		'title'      => __( 'Test Katılımlarım', 'tutor' ),
		'url'        => $mobile_footer_url_2,
		'is_active'  => $mobile_footer_url_2 == $current_url || strpos($current_url, 'my-quiz-attempts') !== false,
		'icon_class' => 'ttr tutor-icon-quiz',
	),
);

do_action( 'tutor_dashboard/before/wrap' );
?>
<div class="tutor-wrap tutor-wrap-parent tutor-dashboard tutor-frontend-dashboard tutor-dashboard-student tutor-pb-80">
	<div class="tutor-container">
		<div class="tutor-row tutor-d-flex tutor-justify-between tutor-frontend-dashboard-header">
			<!-- Hamburger Menu Button ve Sayfa Başlığı -->
			<div class="tutor-header-nav-section hamburger-container" style="position: absolute; top: 15px; left: 50px; display: flex; align-items: center; z-index: 9999;">
				<button class="tutor-hamburger-menu-btn" type="button" style="background: none !important; border: none !important; width: 40px !important; height: 40px !important; display: flex !important; align-items: center !important; justify-content: center !important; color: #333 !important; cursor: pointer !important; padding: 8px !important; margin-right: 15px;">
					<svg width="24" height="24" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round" style="pointer-events: none;">
						<line x1="3" y1="6" x2="21" y2="6"></line>
						<line x1="3" y1="12" x2="21" y2="12"></line>
						<line x1="3" y1="18" x2="21" y2="18"></line>
					</svg>
				</button>
				<h2 class="tutor-current-page-title" style="margin: 0; font-size: 18px; font-weight: 600; color: #333;">
					<?php
					// Tutor LMS'in kendi routing sistemini kullan - zaten tanımlanmış $dashboard_page_slug değişkenini kullan
					$current_page = !empty($dashboard_page_slug) ? $dashboard_page_slug : 'dashboard';
					$page_titles = array(
						'dashboard' => 'Kontrol Paneli',
						'enrolled-courses' => 'Kayıtlı Kurslar',
						'active-courses' => 'Aktif Kurslar',
						'completed-courses' => 'Tamamlanan Kurslar',
						'course-wishlist' => 'İstek Listesi',
						'reviews' => 'Değerlendirmeler',
						'my-quiz-attempts' => 'Test Katılımlarım',
						'quiz-attempts' => 'Test Katılımlarım',
						'purchase_history' => 'Satın Alma Geçmişi',
						'my-profile' => 'Profilim',
						'create-course' => 'Kurs Oluştur',
						'my-courses' => 'Kurslarım',
						'announcements' => 'Duyurular',
						'question-answer' => 'Soru & Cevap',
						'assignments' => 'Ödevler',
						'withdraw' => 'Para Çekme',
						'earning' => 'Kazançlar',
						'statements' => 'Hesap Özetleri',
						'settings' => 'Ayarlar',
						'kurs-duyuruları' => 'Kurs Duyuruları'
					);
					echo isset($page_titles[$current_page]) ? esc_html($page_titles[$current_page]) : esc_html(ucfirst(str_replace('-', ' ', $current_page)));
					?>
				</h2>
			</div>
			<div class="tutor-header-left-side tutor-dashboard-header tutor-col-md-6 tutor-d-flex tutor-align-center" style="border: none;">
				<div class="tutor-user-info">
					<?php
					$instructor_rating = tutor_utils()->get_instructor_ratings( $user->ID );

					if ( current_user_can( tutor()->instructor_role ) ) {
						?>
						<div class="tutor-fs-4 tutor-fw-medium tutor-color-black tutor-dashboard-header-username">
							<?php echo esc_html( $user->display_name ); ?>
						</div>
						<div class="tutor-dashboard-header-stats">
							<div class="tutor-dashboard-header-ratings">
								<?php tutor_utils()->star_rating_generator_v2( $instructor_rating->rating_avg, $instructor_rating->rating_count, true ); ?>
							</div>
						</div>
						<?php
					} else {
						?>
						<div class="tutor-dashboard-header-display-name tutor-color-black">
							<div class="tutor-fs-4 tutor-fw-medium tutor-dashboard-header-username">
								<?php echo esc_html( $user->display_name ); ?>
							</div>
						</div>
						<?php
					}
					?>
				</div>
			</div>
			<div class="tutor-header-right-side tutor-col-md-6 tutor-d-flex tutor-justify-end tutor-mt-20 tutor-mt-md-0">
				<div class="tutor-dashboard-header-avatar" style="margin-right: 15px;">
					<?php
					tutor_utils()->get_tutor_avatar( $user_id, 'xl', true )
					?>
					<!-- Avatar Dropdown Menü -->
					<div class="tutor-avatar-dropdown">
						<div class="tutor-avatar-dropdown-header">
							<?php tutor_utils()->get_tutor_avatar( $user_id, 'xl', true ) ?>
							<div class="tutor-user-info">
								<p class="tutor-user-name"><?php echo esc_html( $user->display_name ); ?></p>
								<p class="tutor-user-role"><?php echo current_user_can( tutor()->instructor_role ) ? esc_html__( 'Eğitmen', 'tutor' ) : esc_html__( 'Öğrenci', 'tutor' ); ?></p>
							</div>
						</div>
						<div class="tutor-avatar-dropdown-stats">
							<?php if ( current_user_can( tutor()->instructor_role ) ) : ?>
								<div class="tutor-dashboard-header-ratings">
									<?php tutor_utils()->star_rating_generator_v2( $instructor_rating->rating_avg, $instructor_rating->rating_count, true ); ?>
								</div>
							<?php endif; ?>
						</div>
						<ul class="tutor-avatar-dropdown-menu">
							<li>
								<a href="<?php echo esc_url( tutor_utils()->tutor_dashboard_url( 'my-profile' ) ); ?>" class="tutor-dashboard-menu-item-link">
									<i class="tutor-icon-user-bold tutor-dashboard-menu-item-icon"></i>
									<span class="tutor-dashboard-menu-item-text tutor-ml-12">Profilim</span>
								</a>
							</li>
							<li>
								<a href="<?php echo esc_url( tutor_utils()->tutor_dashboard_url( 'settings' ) ); ?>" class="tutor-dashboard-menu-item-link">
									<i class="tutor-icon-gear tutor-dashboard-menu-item-icon"></i>
									<span class="tutor-dashboard-menu-item-text tutor-ml-12">Ayarlar</span>
								</a>
							</li>
							<li class="tutor-avatar-dropdown-divider"></li>
							<li>
								<a href="<?php echo esc_url( tutor_utils()->tutor_dashboard_url( 'logout' ) ); ?>" class="tutor-dashboard-menu-item-link">
									<i class="tutor-icon-signout tutor-dashboard-menu-item-icon"></i>
									<span class="tutor-dashboard-menu-item-text tutor-ml-12">Çıkış Yap</span>
								</a>
							</li>
						</ul>
					</div>
				</div>
				<div class="tutor-d-flex tutor-align-center">
					<?php
					// Eğitmen Paneli butonu - Sadece eğitmen rolüne sahip kullanıcılarda görünür
					if ( current_user_can( tutor()->instructor_role ) ) {
						?>
						<a href="http://localhost/wordpress/wp-admin/" class="tutor-btn tutor-btn-outline-primary" target="_blank" style="margin-right: 15px;">
							<i class="tutor-icon-edit"></i> &nbsp; <?php esc_html_e( 'Eğitmen Paneli', 'tutor' ); ?>
						</a>
						<?php
					}

					do_action( 'tutor_dashboard/before_header_button' );
					$instructor_status  = tutor_utils()->instructor_status( 0, false );
					$instructor_status  = is_string( $instructor_status ) ? strtolower( $instructor_status ) : '';
					$rejected_on        = get_user_meta( $user->ID, '_is_tutor_instructor_rejected', true );
					$info_style         = 'vertical-align: middle; margin-right: 7px;';
					$info_message_style = 'display:inline-block; color:#7A7A7A; font-size: 15px;';

					ob_start();
					if ( tutor_utils()->get_option( 'enable_become_instructor_btn' ) ) {
						?>
						<a id="tutor-become-instructor-button" class="tutor-btn tutor-btn-outline-primary" href="<?php echo esc_url( tutor_utils()->instructor_register_url() ); ?>">
							<i class="tutor-icon-user-bold"></i> &nbsp; <?php esc_html_e( 'Become an instructor', 'tutor' ); ?>
						</a>
						<?php
					}
					$become_button = ob_get_clean();

					if ( current_user_can( tutor()->instructor_role ) ) {
						$course_type = tutor()->course_post_type;
						?>
						<?php
						/**
						 * Render create course button based on free & pro
						 *
						 * @since v2.0.7
						 */
						if ( function_exists( 'tutor_pro' ) ) :
							?>
							<?php do_action( 'tutor_course_create_button' ); ?>
							<?php else : ?>
							<!-- Kurs Oluştur butonu gizlendi -->
					<?php endif; ?>
						<?php
					} elseif ( 'pending' === $instructor_status ) {
						$on = get_user_meta( $user->ID, '_is_tutor_instructor', true );
						$on = gmdate( 'd F, Y', $on );
						echo '<span style="' . esc_attr( $info_message_style ) . '">
                                    <i class="dashicons dashicons-info tutor-color-warning" style=" ' . esc_attr( $info_style ) . '"></i>',
						esc_html__( 'Your Application is pending as of', 'tutor' ), ' <b>', esc_html( $on ), '</b>',
						'</span>';
					} elseif ( $rejected_on || 'blocked' !== $instructor_status ) {
						echo $become_button; //phpcs:ignore --data escaped above
					}
					?>
				</div>
			</div>
		</div>

		<div class="tutor-row tutor-frontend-dashboard-maincontent">
			<div class="tutor-col-12 tutor-col-md-4 tutor-col-lg-3 tutor-dashboard-left-menu">
				<?php
					// Avatar alanını sidebar'a ekle
					$user_id = get_current_user_id();
					$user = get_userdata($user_id);
				?>
				<div class="tutor-sidebar-avatar-container">
					<?php tutor_utils()->get_tutor_avatar( $user_id, 'xl', true ); ?>
					<div class="tutor-sidebar-user-info">
						<div class="tutor-sidebar-user-name"><?php echo esc_html( $user->display_name ); ?></div>
						<div class="tutor-sidebar-user-email"><?php echo esc_html( $user->user_email ); ?></div>
					</div>
				</div>
				<style>
					.tutor-dashboard-logo {
						background-color: #f8f9fa;
						border-radius: 8px;
						display: flex;
						justify-content: center;
						align-items: center;
						overflow: hidden;
					}
					.tutor-dashboard-logo:hover {
						/* background-color: #e9ecef; */
						/* box-shadow: 0 2px 5px rgba(0, 0, 0, 0.1); */
					}
					.tutor-dashboard-logo img {
						max-width: 100%;
						max-height: 100%;
						object-fit: contain;
					}
					@media (max-width: 991px) {
						.tutor-dashboard-logo {
							width: 180px !important;
							height: 50px !important;
						}
					}
				</style>
				<ul class="tutor-dashboard-permalinks">
					<?php
					$dashboard_pages = tutor_utils()->tutor_dashboard_nav_ui_items();
					// get reviews settings value.
					$disable = ! get_tutor_option( 'enable_course_review' );
					foreach ( $dashboard_pages as $dashboard_key => $dashboard_page ) {
						/**
						 * If not enable from settings then quit
						 *
						 *  @since v2.0.0
						 */
						if ( $disable && 'reviews' === $dashboard_key ) {
							continue;
						}

						$menu_title = $dashboard_page;
						$menu_link  = tutor_utils()->get_tutor_dashboard_page_permalink( $dashboard_key );
						$separator  = false;
						$menu_icon  = '';

						if ( is_array( $dashboard_page ) ) {
							$menu_title     = tutor_utils()->array_get( 'title', $dashboard_page );
							$menu_icon_name = tutor_utils()->array_get( 'icon', $dashboard_page, ( isset( $dashboard_page['icon'] ) ? $dashboard_page['icon'] : '' ) );
							if ( $menu_icon_name ) {
								$menu_icon = "<span class='{$menu_icon_name} tutor-dashboard-menu-item-icon'></span>";
							}
							// Add new menu item property "url" for custom link.
							if ( isset( $dashboard_page['url'] ) ) {
								$menu_link = $dashboard_page['url'];
							}
							if ( isset( $dashboard_page['type'] ) && 'separator' === $dashboard_page['type'] ) {
								$separator = true;
							}
						}
						if ( $separator ) {
							echo '<li class="tutor-dashboard-menu-divider"></li>';
							if ( $menu_title ) {
								?>
								<li class='tutor-dashboard-menu-divider-header'>
									<?php echo esc_html( $menu_title ); ?>
								</li>
								<?php
							}
						} else {
							$li_class = "tutor-dashboard-menu-{$dashboard_key}";
							if ( 'index' === $dashboard_key ) {
								$dashboard_key = '';
							}
							$active_class    = $dashboard_key == $dashboard_page_slug ? 'active' : '';
							$data_no_instant = 'logout' == $dashboard_key ? 'data-no-instant' : '';
							$menu_link = apply_filters( 'tutor_dashboard_menu_link', $menu_link, $menu_title );
							?>
							<li class='tutor-dashboard-menu-item <?php echo esc_attr( $li_class . ' ' . $active_class ); ?>'>
								<a <?php echo esc_html( $data_no_instant ); ?> href="<?php echo esc_url( $menu_link ); ?>" class='tutor-dashboard-menu-item-link tutor-fs-6 tutor-color-black'>
									<?php
									echo wp_kses(
										$menu_icon,
										tutor_utils()->allowed_icon_tags()
									);
									?>
									<span class='tutor-dashboard-menu-item-text tutor-ml-12'>
										<?php echo esc_html( $menu_title ); ?>
									</span>
								</a>
							</li>
							<?php
						}
					}
					?>
				</ul>
			</div>

			<div class="tutor-col-12 tutor-col-md-8 tutor-col-lg-9">
				<div class="tutor-dashboard-content">
					<?php

					if ( $dashboard_page_name ) {
						do_action( 'tutor_load_dashboard_template_before', $dashboard_page_name );

						/**
						 * Load dashboard template part from other location
						 *
						 * This filter is basically added for adding templates from respective addons
						 *
						 * @since version 1.9.3
						 */
						$other_location      = '';
						$from_other_location = apply_filters( 'load_dashboard_template_part_from_other_location', $other_location );

						if ( '' == $from_other_location ) {
							tutor_load_template( 'dashboard.' . $dashboard_page_name );
						} else {
							// Load template from other location full abspath.
							include_once $from_other_location;
						}

						do_action( 'tutor_load_dashboard_template_after', $dashboard_page_name );
					} else {
						tutor_load_template( 'dashboard.dashboard' );
					}
					?>
				</div>
			</div>
		</div>
	</div>
	<div id="tutor-dashboard-footer-mobile">
		<div class="tutor-container">
			<div class="tutor-row">
				<?php foreach ( $footer_links as $link_item ) : ?>
					<a class="tutor-col-3 <?php echo $link_item['is_active'] ? 'active' : ''; ?>" href="<?php echo esc_url( $link_item['url'] ); ?>">
						<i class="<?php echo esc_attr( $link_item['icon_class'] ); ?>"></i>
						<span><?php echo esc_html( $link_item['title'] ); ?></span>
					</a>
				<?php endforeach; ?>
			</div>
		</div>
	</div>
</div>

<?php do_action( 'tutor_dashboard/after/wrap' ); ?>

<?php
if ( ! $is_by_short_code && ! defined( 'OTLMS_VERSION' ) ) {
    // Özel footer - tema footer'ı olmadan
    ?>
        </div>

        <!-- Sayfa Yükleme Çubuğu JavaScript -->
        <script src="<?php echo plugin_dir_url(__FILE__) . '../assets/js/page-loading-bar.js'; ?>"></script>

        <?php wp_footer(); ?>
    </body>
    </html>
    <?php
}
