<?php
/**
 * Dashboard Question Answer Page
 *
 * @package Tu<PERSON>\Templates
 * @subpackage Dashboard
 * <AUTHOR> <<EMAIL>>
 * @link https://themeum.com
 * @version 1.6.4
 */

use TUTOR\Input;
use TUTOR\Instructor;
use TUTOR\Q_And_A;

$question_id = Input::get( 'question_id', null, Input::TYPE_INT );
if ( $question_id ) {
	$question = tutor_utils()->get_qa_question( $question_id );
	$user_id  = get_current_user_id();

	if ( $question && ! Q_And_A::has_qna_access( $user_id, $question->comment_post_ID ) ) {
		tutor_utils()->tutor_empty_state( tutor_utils()->error_message() );
		return;
	}

	tutor_load_template_from_custom_path(
		tutor()->path . '/views/qna/qna-single.php',
		array(
			'question_id' => $question_id,
			'context'     => 'frontend-dashboard-qna-single',
		)
	);
	return;
}

if ( in_array( Input::get( 'view_as' ), array( 'student', 'instructor' ) ) ) {
	update_user_meta( get_current_user_id(), 'tutor_qa_view_as', Input::get( 'view_as' ) );
}

$is_instructor     = tutor_utils()->is_instructor( null, true );
$view_option       = get_user_meta( get_current_user_id(), 'tutor_qa_view_as', true );
// Her zaman ogrenci gorunumu goster - eğitmen kısmını gizle
$view_as           = 'student';
$as_instructor_url = add_query_arg( array( 'view_as' => 'instructor' ), tutor()->current_url );
$as_student_url    = add_query_arg( array( 'view_as' => 'student' ), tutor()->current_url );
$qna_tabs          = \Tutor\Q_And_A::tabs_key_value( 'student' == $view_as ? get_current_user_id() : null );
$active_tab        = Input::get( 'tab', 'all' );
?>

<div class="tutor-frontend-dashboard-qna-header tutor-mb-32">
	<div class="tutor-row tutor-mb-24">
		<div class="tutor-col">
			<div class="tutor-fs-5 tutor-fw-medium tutor-color-black">
				<?php esc_html_e( 'Question & Answer', 'tutor' ); ?>
			</div>
		</div>

		<div class="tutor-col-auto">
			<button type="button" class="tutor-btn tutor-btn-primary" id="dmr-create-question-btn">
				<span class="tutor-icon-plus tutor-mr-8"></span>
				<?php esc_html_e( 'Soru Oluştur', 'dmr-lms' ); ?>
			</button>
		</div>

		<?php
		// Eğitmen/Öğrenci toggle kısmı gizlendi - sadece öğrenci görünümü gösterilecek
		// if ( $is_instructor ) : ?>
			<!-- Toggle kısmı kaldırıldı -->
		<?php // endif; ?>
	</div>

		<div class="tutor-row">
			<div class="tutor-col-lg-5">
				<div class="tutor-qna-filter tutor-d-flex tutor-align-center">
					<span class="tutor-fs-7 tutor-color-secondary tutor-mr-20"><?php esc_html_e( 'Sort By', 'tutor' ); ?>:</span>
					<div class="tutor-flex-grow-1">
						<select class="tutor-form-select tutor-select-redirector">
							<?php
							foreach ( $qna_tabs as $tab ) {
								$markup = '<option value="' . $tab['url'] . '" ' . ( $active_tab == $tab['key'] ? 'selected="selected"' : '' ) . '>
                                        ' . $tab['title'] . '(' . $tab['value'] . ')' . '
                                    </option>';
								echo wp_kses(
									$markup,
									array(
										'option' => array(
											'value'    => true,
											'selected' => true,
										),
									)
								);
							}
							?>
						</select>
					</div>
				</div>
			</div>
		</div>
</div>

<?php
$per_page     = tutor_utils()->get_option( 'pagination_per_page', 10 );
$current_page = max( 1, tutor_utils()->avalue_dot( 'current_page', $_GET ) );
$offset       = ( $current_page - 1 ) * $per_page;

$q_status    = Input::get( 'tab' );
// Her zaman sadece mevcut kullanıcının sorularını goster (ogrenci gorunumu)
$asker_id    = get_current_user_id();
$total_items = tutor_utils()->get_qa_questions( $offset, $per_page, '', null, null, $asker_id, $q_status, true );
$questions   = tutor_utils()->get_qa_questions( $offset, $per_page, '', null, null, $asker_id, $q_status );

tutor_load_template_from_custom_path(
	tutor()->path . '/views/qna/qna-table.php',
	array(
		'qna_list'       => $questions,
		'context'        => 'frontend-dashboard-qna-table-' . $view_as,
		'view_as'        => $view_as,
		'qna_pagination' => array(
			'base'        => '?current_page=%#%',
			'total_items' => $total_items,
			'per_page'    => $per_page,
			'paged'       => $current_page,
		),
	)
);
?>

<!-- Soru Oluşturma Modal Penceresi -->
<div id="dmr-create-question-modal" class="tutor-modal tutor-modal-scrollable" style="display: none;">
	<div class="tutor-modal-overlay"></div>
	<div class="tutor-modal-window">
		<div class="tutor-modal-content">
			<div class="tutor-modal-header">
				<div class="tutor-modal-title">
					<h4><?php esc_html_e( 'Yeni Soru Oluştur', 'dmr-lms' ); ?></h4>
				</div>
				<button class="tutor-iconic-btn tutor-modal-close" type="button">
					<span class="tutor-icon-times" area-hidden="true"></span>
				</button>
			</div>

			<div class="tutor-modal-body">
				<form id="dmr-create-question-form">
					<div class="tutor-mb-32">
						<label class="tutor-form-label">
							<?php esc_html_e( 'Kurs Seçin', 'dmr-lms' ); ?>
						</label>
						<select name="course_id" id="dmr-question-course-select" class="tutor-form-select" required>
							<option value=""><?php esc_html_e( 'Bir kurs seçin...', 'dmr-lms' ); ?></option>
							<?php
							// Kullanıcının kayıtlı olduğu kursları getir
							$user_id = get_current_user_id();
							$enrolled_courses = tutor_utils()->get_enrolled_courses_by_user( $user_id );

							if ( $enrolled_courses && $enrolled_courses->have_posts() ) {
								while ( $enrolled_courses->have_posts() ) {
									$enrolled_courses->the_post();
									$course_id = get_the_ID();
									$course_title = get_the_title();

									// Kurs için Q&A etkin mi kontrol et
									$disable_qa_for_this_course = get_post_meta( $course_id, '_tutor_enable_qa', true ) != 'yes';
									$enable_q_and_a_on_course = tutor_utils()->get_option( 'enable_q_and_a_on_course' );

									if ( $enable_q_and_a_on_course && ! $disable_qa_for_this_course ) {
										echo '<option value="' . esc_attr( $course_id ) . '">' . esc_html( $course_title ) . '</option>';
									}
								}
								wp_reset_postdata();
							}
							?>
						</select>
					</div>

					<div class="tutor-mb-32">
						<label class="tutor-form-label">
							<?php esc_html_e( 'Sorunuz', 'dmr-lms' ); ?>
						</label>
						<textarea name="question_content" id="dmr-question-content" class="tutor-form-control" rows="6" placeholder="<?php esc_attr_e( 'Sorunuzu buraya yazın...', 'dmr-lms' ); ?>" required></textarea>
					</div>

					<div class="tutor-d-flex tutor-justify-end">
						<button type="button" class="tutor-btn tutor-btn-outline-primary tutor-mr-16" id="dmr-cancel-question-btn">
							<?php esc_html_e( 'İptal', 'dmr-lms' ); ?>
						</button>
						<button type="submit" class="tutor-btn tutor-btn-primary" id="dmr-submit-question-btn">
							<span class="tutor-icon-paper-plane tutor-mr-8"></span>
							<?php esc_html_e( 'Gönder', 'dmr-lms' ); ?>
						</button>
					</div>
				</form>
			</div>
		</div>
	</div>
</div>

<script>
jQuery(document).ready(function($) {
	// Modal açma
	$('#dmr-create-question-btn').on('click', function() {
		$('#dmr-create-question-modal').fadeIn(300);
		$('body').addClass('tutor-modal-is-open');
	});

	// Modal kapatma
	function closeModal() {
		$('#dmr-create-question-modal').fadeOut(300);
		$('body').removeClass('tutor-modal-is-open');
		// Formu temizle
		$('#dmr-create-question-form')[0].reset();
	}

	$('#dmr-cancel-question-btn, .tutor-modal-close, .tutor-modal-overlay').on('click', closeModal);

	// ESC tuşu ile modal kapatma
	$(document).on('keydown', function(e) {
		if (e.keyCode === 27 && $('#dmr-create-question-modal').is(':visible')) {
			closeModal();
		}
	});

	// Form gönderme
	$('#dmr-create-question-form').on('submit', function(e) {
		e.preventDefault();

		var $form = $(this);
		var $submitBtn = $('#dmr-submit-question-btn');
		var originalText = $submitBtn.html();

		// Buton durumunu değiştir
		$submitBtn.prop('disabled', true).html('<span class="tutor-icon-spinner tutor-mr-8"></span>Gönderiliyor...');

		// Form verilerini al
		var formData = {
			action: 'dmr_create_question',
			course_id: $('#dmr-question-course-select').val(),
			question_content: $('#dmr-question-content').val(),
			_wpnonce: '<?php echo wp_create_nonce( 'dmr_create_question_nonce' ); ?>'
		};

		// AJAX isteği gönder
		$.ajax({
			url: '<?php echo admin_url( 'admin-ajax.php' ); ?>',
			type: 'POST',
			data: formData,
			success: function(response) {
				if (response.success) {
					// Başarılı mesaj göster
					tutor_toast('Başarılı', response.data.message, 'success');

					// Modal'ı kapat
					closeModal();

					// Sayfayı yenile
					setTimeout(function() {
						window.location.reload();
					}, 1000);
				} else {
					// Hata mesajı göster
					tutor_toast('Hata', response.data.message || 'Bir hata oluştu', 'error');
				}
			},
			error: function() {
				tutor_toast('Hata', 'Sunucu hatası oluştu', 'error');
			},
			complete: function() {
				// Buton durumunu eski haline getir
				$submitBtn.prop('disabled', false).html(originalText);
			}
		});
	});
});
</script>
