/**
 * <PERSON><PERSON> Geçiş Yükleme Çubuğu JavaScript
 * YouTube tarzı progress bar ile sayfa geçiş efektleri
 */

(function() {
    'use strict';

    // Yükleme çubuğu sınıfı
    class PageLoadingBar {
        constructor() {
            this.progressBar = null;
            this.progressElement = null;
            this.currentProgress = 0;
            this.targetProgress = 0;
            this.isActive = false;
            this.animationFrame = null;
            this.startTime = null;
            this.loadingTimeout = null;
            
            this.init();
        }

        // Başlatma
        init() {
            this.createProgressBar();
            this.bindEvents();
            console.log('Sayfa yükleme çubuğu başlatıldı');
        }

        // Progress bar HTML elementini oluştur
        createProgressBar() {
            // Mevcut progress bar varsa kaldır
            const existing = document.querySelector('.page-loading-bar');
            if (existing) {
                existing.remove();
            }

            // Yeni progress bar oluştur
            this.progressBar = document.createElement('div');
            this.progressBar.className = 'page-loading-bar';
            this.progressBar.innerHTML = `
                <div class="progress-bar"></div>
                <div class="loading-text" style="display: none;">Yükleniyor...</div>
            `;

            // Progress element referansını al
            this.progressElement = this.progressBar.querySelector('.progress-bar');

            // Body'ye ekle
            document.body.appendChild(this.progressBar);
        }

        // Event listener'ları bağla
        bindEvents() {
            // Sayfa yükleme olayları
            window.addEventListener('beforeunload', () => this.start());
            window.addEventListener('load', () => this.complete());
            
            // AJAX istekleri için
            this.interceptAjaxRequests();
            
            // Link tıklamaları için
            this.interceptLinkClicks();
            
            // Form gönderimi için
            this.interceptFormSubmissions();
            
            // History API için
            this.interceptHistoryAPI();
            
            // Tutor LMS spesifik olayları
            this.bindTutorEvents();
        }

        // AJAX isteklerini yakala
        interceptAjaxRequests() {
            // XMLHttpRequest'i yakala
            const originalXHR = window.XMLHttpRequest;
            const self = this;
            
            window.XMLHttpRequest = function() {
                const xhr = new originalXHR();
                
                xhr.addEventListener('loadstart', () => {
                    self.start();
                });
                
                xhr.addEventListener('loadend', () => {
                    self.complete();
                });
                
                return xhr;
            };

            // Fetch API'yi yakala
            if (window.fetch) {
                const originalFetch = window.fetch;
                window.fetch = function(...args) {
                    self.start();
                    return originalFetch.apply(this, args)
                        .then(response => {
                            self.complete();
                            return response;
                        })
                        .catch(error => {
                            self.error();
                            throw error;
                        });
                };
            }

            // jQuery AJAX'ı yakala
            if (window.jQuery) {
                const $ = window.jQuery;
                $(document).ajaxStart(() => this.start());
                $(document).ajaxComplete(() => this.complete());
                $(document).ajaxError(() => this.error());
            }
        }

        // Link tıklamalarını yakala
        interceptLinkClicks() {
            document.addEventListener('click', (e) => {
                const link = e.target.closest('a');
                if (link && this.shouldInterceptLink(link)) {
                    this.start();
                }
            });
        }

        // Form gönderimlerini yakala
        interceptFormSubmissions() {
            document.addEventListener('submit', (e) => {
                if (e.target.tagName === 'FORM') {
                    this.start();
                }
            });
        }

        // History API'yi yakala
        interceptHistoryAPI() {
            const originalPushState = history.pushState;
            const originalReplaceState = history.replaceState;
            const self = this;

            history.pushState = function(...args) {
                self.start();
                const result = originalPushState.apply(this, args);
                setTimeout(() => self.complete(), 100);
                return result;
            };

            history.replaceState = function(...args) {
                self.start();
                const result = originalReplaceState.apply(this, args);
                setTimeout(() => self.complete(), 100);
                return result;
            };

            window.addEventListener('popstate', () => {
                this.start();
                setTimeout(() => this.complete(), 100);
            });
        }

        // Tutor LMS spesifik olayları
        bindTutorEvents() {
            // Dashboard sayfa geçişleri
            document.addEventListener('click', (e) => {
                const dashboardLink = e.target.closest('.tutor-dashboard-menu-item-link');
                if (dashboardLink) {
                    this.start();
                }
            });

            // Kurs sayfası geçişleri
            document.addEventListener('click', (e) => {
                const courseLink = e.target.closest('.tutor-course-card a, .tutor-course-name a');
                if (courseLink) {
                    this.start();
                }
            });
        }

        // Link'in yakalanıp yakalanmayacağını kontrol et
        shouldInterceptLink(link) {
            const href = link.getAttribute('href');
            
            // Boş href veya anchor link
            if (!href || href.startsWith('#')) return false;
            
            // External link
            if (href.startsWith('http') && !href.includes(window.location.hostname)) return false;
            
            // Download link
            if (link.hasAttribute('download')) return false;
            
            // Target blank
            if (link.getAttribute('target') === '_blank') return false;
            
            // JavaScript link
            if (href.startsWith('javascript:')) return false;
            
            // Mail link
            if (href.startsWith('mailto:')) return false;
            
            return true;
        }

        // Yükleme çubuğunu başlat
        start() {
            if (this.isActive) return;
            
            this.isActive = true;
            this.currentProgress = 0;
            this.targetProgress = 10;
            this.startTime = Date.now();
            
            // CSS sınıflarını ekle
            this.progressBar.classList.add('active', 'starting');
            
            // İlk animasyonu başlat
            this.updateProgress();
            
            // Otomatik ilerleme
            this.autoProgress();
            
            console.log('Yükleme çubuğu başlatıldı');
        }

        // Otomatik ilerleme
        autoProgress() {
            this.loadingTimeout = setTimeout(() => {
                if (this.isActive && this.targetProgress < 90) {
                    this.targetProgress += Math.random() * 30;
                    if (this.targetProgress > 90) this.targetProgress = 90;
                    this.autoProgress();
                }
            }, 200 + Math.random() * 300);
        }

        // Progress'i güncelle
        updateProgress() {
            if (!this.isActive) return;
            
            // Smooth interpolation
            const diff = this.targetProgress - this.currentProgress;
            this.currentProgress += diff * 0.1;
            
            // Progress bar'ı güncelle
            this.progressElement.style.width = this.currentProgress + '%';
            
            // Animasyonu devam ettir
            this.animationFrame = requestAnimationFrame(() => this.updateProgress());
        }

        // Yükleme tamamlandı
        complete() {
            if (!this.isActive) return;
            
            this.targetProgress = 100;
            this.progressBar.classList.add('complete');
            this.progressBar.classList.remove('starting');
            
            // Timeout'ları temizle
            if (this.loadingTimeout) {
                clearTimeout(this.loadingTimeout);
                this.loadingTimeout = null;
            }
            
            // Tamamlanma animasyonu
            setTimeout(() => {
                this.progressElement.style.width = '100%';
                
                setTimeout(() => {
                    this.reset();
                }, 500);
            }, 100);
            
            console.log('Yükleme çubuğu tamamlandı');
        }

        // Hata durumu
        error() {
            if (!this.isActive) return;
            
            this.progressBar.classList.add('error');
            this.progressBar.classList.remove('starting');
            
            setTimeout(() => {
                this.reset();
            }, 1000);
            
            console.log('Yükleme çubuğu hatası');
        }

        // Reset
        reset() {
            this.isActive = false;
            this.currentProgress = 0;
            this.targetProgress = 0;
            
            // Animasyonu durdur
            if (this.animationFrame) {
                cancelAnimationFrame(this.animationFrame);
                this.animationFrame = null;
            }
            
            // Timeout'ları temizle
            if (this.loadingTimeout) {
                clearTimeout(this.loadingTimeout);
                this.loadingTimeout = null;
            }
            
            // CSS sınıflarını temizle
            this.progressBar.classList.remove('active', 'complete', 'error', 'starting');
            this.progressElement.style.width = '0%';
        }

        // Manuel progress ayarlama
        setProgress(progress) {
            if (this.isActive) {
                this.targetProgress = Math.max(0, Math.min(100, progress));
            }
        }

        // Yok et
        destroy() {
            this.reset();
            if (this.progressBar && this.progressBar.parentNode) {
                this.progressBar.parentNode.removeChild(this.progressBar);
            }
        }
    }

    // Sayfa yüklendiğinde başlat
    if (document.readyState === 'loading') {
        document.addEventListener('DOMContentLoaded', () => {
            window.pageLoadingBar = new PageLoadingBar();
        });
    } else {
        window.pageLoadingBar = new PageLoadingBar();
    }

    // Global erişim için
    window.PageLoadingBar = PageLoadingBar;

})();
