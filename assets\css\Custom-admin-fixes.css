/**
 * Custom Admin Fixes CSS
 * Bu dosya, WordPress admin panelindeki Tutor LMS menüsünde bulunan
 * "Pro'ya yükselt" menü öğesini gizlemek için kullanılır.
 * Ayrıca kullanıcı profil sayfasındaki "Araç çubuğu" yazısını ve seçeneğini gizler.
 *
 * @package DmrLMS
 * @since 1.0.1
 */

/* "Pro'ya yükselt" menü öğesini gizle */
#adminmenu li#toplevel_page_tutor a[href="https://www.themeum.com/product/tutor-lms/"],
#adminmenu li#toplevel_page_tutor a[href*="tutor-pro-page"],
#adminmenu li#toplevel_page_tutor ul.wp-submenu li a[href*="tutor-pro-page"],
#adminmenu li#toplevel_page_tutor ul.wp-submenu li:last-child {
    display: none !important;
}

/* Tutor LMS menüsündeki diğer öğelerin stillerini düzelt */
#adminmenu li#toplevel_page_tutor ul.wp-submenu {
    padding-bottom: 5px !important;
}

/* Kullanıcı profil sayfasındaki "Araç çubuğu" yazısını ve seçeneğini gizle */
/* NOT: Bu CSS kuralları artık PHP tarafında dinamik olarak ekleniyor */
/* Sadece admin olmayan kullanıcılar için araç çubuğu seçeneği gizlenir */
/* Admin kullanıcıları araç çubuğu seçeneğini görebilir ve değiştirebilir */

/* Dashboard sidebar'ındaki eğitmen menü öğelerini gizle */
.tutor-dashboard-menu-item.tutor-dashboard-menu-my-courses,
.tutor-dashboard-menu-item.tutor-dashboard-menu-announcements,
.tutor-dashboard-menu-item.tutor-dashboard-menu-withdraw,
.tutor-dashboard-menu-item.tutor-dashboard-menu-quiz-attempts,
.tutor-dashboard-menu-divider-header {
    display: none !important;
    visibility: hidden !important;
}

/* CSS seçicileri ile de gizle (ek güvenlik) */
#tutor-page-wrap .tutor-dashboard-left-menu .tutor-dashboard-menu-item.tutor-dashboard-menu-my-courses,
#tutor-page-wrap .tutor-dashboard-left-menu .tutor-dashboard-menu-item.tutor-dashboard-menu-announcements,
#tutor-page-wrap .tutor-dashboard-left-menu .tutor-dashboard-menu-item.tutor-dashboard-menu-withdraw,
#tutor-page-wrap .tutor-dashboard-left-menu .tutor-dashboard-menu-item.tutor-dashboard-menu-quiz-attempts,
#tutor-page-wrap .tutor-dashboard-left-menu .tutor-dashboard-menu-divider-header {
    display: none !important;
    visibility: hidden !important;
}
